import { generateClient } from 'aws-amplify/api';
import { getDevicePlatform } from '../utils/utils';
import { createUserFeedback } from '../graphql/mutations';

const client = generateClient();
const AUTH_MODE = process.env.AUTH_MODE || 'apiKey';

export const createNewFeedback = async (input) => {
  try {
    const data = {
      ...input,
      platform: getDevicePlatform(),
      release: process.env.RELEASE,
      createdAt: new Date(),
    };
    const response = await client.graphql({
      query: createUserFeedback,
      variables: { input: data },
      authMode: AUTH_MODE,
    });
    console.log('User feedback saved:', response.data.createUserFeedback);
  } catch (error) {
    console.error('Error saving user feedback:', error);
    throw error;
  }
};
