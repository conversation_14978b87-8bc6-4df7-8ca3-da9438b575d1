import { createProfile, updateProfile } from '../graphql/mutations';
import { generateClient } from 'aws-amplify/api';
import { getDevicePlatform } from '../utils/utils';
import { getProfile } from '../graphql/queries';

const client = generateClient();
const AUTH_MODE = process.env.AUTH_MODE || 'apiKey';

export const createUserProfile = async (profileData) => {
  try {
    const { profileNotCreated, ...profile } = profileData;
    const data = {
      ...profile,
      initialDose: JSON.stringify(profile.initialDose || {}),
      confirmationCodeCount: JSON.stringify({}),
      confirmationCodeSentAt: new Date(),
      release: process.env.RELEASE,
      platform: getDevicePlatform(),
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    const response = await client.graphql({
      query: createProfile,
      variables: { input: data },
      authMode: AUTH_MODE,
    });

    console.log('Profile created:', response.data.createProfile);
  } catch (error) {
    console.error('Error creating profile:', error);
    throw error;
  }
};

export const updateUserProfile = async (profileData) => {
  try {
    const { profileNotCreated, ...profile } = profileData;
    const data = {
      ...profile,
      initialDose: JSON.stringify(profile.initialDose || {}),
      confirmationCodeCount: JSON.stringify({}),
      updatedAt: new Date(),
    };

    const response = await client.graphql({
      query: updateProfile,
      variables: { input: data },
      authMode: AUTH_MODE,
    });

    console.log('Profile updated:', response.data.updateProfile);
  } catch (error) {
    console.error('Error updating profile:', error);
  }
};

export const getUserProfile = async (userId) => {
  try {
    const response = await client.graphql({
      query: getProfile,
      variables: { userId },
      authMode: AUTH_MODE,
    });
    console.log('Profile retrieved:', response.data.getProfile);
    return response.data.getProfile;
  } catch (error) {
    console.error('Error retrieving profile:', error);
    throw error;
  }
};

export const updateUserDetails = async (input) => {
  const data = {
    ...input,
    updatedAt: new Date(),
  };

  try {
    const result = await client.graphql({
      query: updateProfile,
      variables: { input: data },
      authMode: AUTH_MODE,
    });
    console.log('User details updated successfully:', result);
  } catch (error) {
    console.error('Error updating user details:', error);
  }
};
