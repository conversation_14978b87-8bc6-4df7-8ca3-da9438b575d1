import { generateClient } from 'aws-amplify/api';
import { createDeleteRequest } from '../graphql/mutations';

const client = generateClient();
const AUTH_MODE = process.env.AUTH_MODE || 'apiKey';

export const deleteAccount = async (params) => {
  try {
    const response = await client.graphql({
      query: createDeleteRequest,
      variables: { input: params },
      authMode: AUTH_MODE,
    });
    console.log('Delete request created:', response.data.createDeleteRequest);
    return response.data.createDeleteRequest;
  } catch (error) {
    console.error('Error creating delete request:', error);
    throw error;
  }
};
