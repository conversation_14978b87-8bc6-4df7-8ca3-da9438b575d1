import { generateClient } from 'aws-amplify/api';
import { listReports } from '../graphql/queries';
import { createReport } from '../graphql/mutations';

const client = generateClient();
const AUTH_MODE = process.env.AUTH_MODE || 'apiKey';

export const createUserReport = async (username) => {
  try {
    const data = {
      username,
      createdAt: new Date(),
      reportType: 'user_report',
    };
    const response = await client.graphql({
      query: createReport,
      variables: { input: data },
      authMode: AUTH_MODE,
    });
    console.log('Report created:', response.data.createReport);
  } catch (error) {
    console.error('Error creating report:', error);
    throw error;
  }
};

export const listUserReports = async (username) => {
  try {
    const response = await client.graphql({
      query: listReports,
      authMode: AUTH_MODE,
      variables: {
        filter: {
          username: { eq: username },
        },
      },
    });
    console.log('Reports retrieved:', response.data.listReports.items.length);
    return response.data.listReports.items;
  } catch (error) {
    console.error('Error retrieving user reports:', error);
    throw error;
  }
};
