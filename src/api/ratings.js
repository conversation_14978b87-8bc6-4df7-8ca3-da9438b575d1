import { generateClient } from 'aws-amplify/api';
import { userRatingsByUserId, userRatingsByUsername } from '../graphql/queries';
import { getDevicePlatform } from '../utils/utils';
import { createUserRating } from '../graphql/mutations';

const client = generateClient();
const AUTH_MODE = process.env.AUTH_MODE || 'apiKey';

export const createNewRating = async (input) => {
  try {
    const data = {
      ...input,
      platform: getDevicePlatform(),
      release: process.env.RELEASE,
      createdAt: new Date(),
    };
    const response = await client.graphql({
      query: createUserRating,
      variables: { input: data },
      authMode: AUTH_MODE,
    });
    console.log('User rating created:', response.data.createUserRating);
  } catch (error) {
    console.error('Error creating user rating:', error);
    throw error;
  }
};

export const listUserRatings = async (userId) => {
  try {
    const response = await client.graphql({
      query: userRatingsByUserId,
      authMode: AUTH_MODE,
      variables: { userId },
    });
    console.log(
      'User ratings retrieved:',
      response.data.userRatingsByUserId.items.length,
    );
    return response.data.userRatingsByUserId.items;
  } catch (error) {
    console.error('Error retrieving user ratings:', error);
    throw error;
  }
};
