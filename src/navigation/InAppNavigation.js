import { createNativeStackNavigator } from '@react-navigation/native-stack';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import MainIntro from '../screens/intro/mainIntro';
import WarningScreen from '../screens/warning/warningScreen';
import NotificationPermission from '../screens/notification/notificationPermission';
import SignUp from '../screens/auth/signUp';
import ConfirmSignUp from '../screens/auth/confirmSignUp';
import ResendSignUpCode from '../screens/auth/resendSignUpCode';
import Login from '../screens/auth/login';
import ResetPassword from '../screens/auth/resetPassword';
import VerifyNewPassword from '../screens/auth/verifyNewPassword';
import WelcomeScreen from '../screens/welcomeScreen/welcomeScreen';
import Step1 from '../screens/profileBuilding/steps/step1';
import Step2 from '../screens/profileBuilding/steps/step2';
import Step3 from '../screens/profileBuilding/steps/step3';
import Step4 from '../screens/profileBuilding/steps/step4';
import Step5 from '../screens/profileBuilding/steps/step5';
import Step6 from '../screens/profileBuilding/steps/step6';
import Step7 from '../screens/profileBuilding/steps/step7';
import SideEffects from '../screens/checkIn/sideEffects.js';
import OverallSideEffect from '../screens/checkIn/overallSideEffect';
import OverallEffect from '../screens/checkIn/overallEffect.js';
import IndividualEffects from '../screens/checkIn/individualEffects.js';
import LastDose from '../screens/checkIn/lastDose';
import CloseAppWarning from '../components/CloseAppWarning';
import { ypdGreen, ypdTeal, ypdWhite } from '../utils/colors';
import HomeIcon from '../assets/svgs/home/<USER>';
import ProgressIcon from '../assets/svgs/home/<USER>';
import SupportIcon from '../assets/svgs/home/<USER>';
import HomeScreen from '../screens/home/<USER>';
import SettingsIcon from '../assets/svgs/checkin/SettingsIcon.js';
import ConfirmRash from '../components/ConfirmRash.js';
import StreakModal from '../components/StreakModal.js';
import { isSmallDevice } from '../utils.js';
import CalendarModal from '../components/CalendarModal.js';
import DeviceInfo from 'react-native-device-info';
import MaxModalities from '../screens/home/<USER>';
import ProgressScreen from '../screens/progress/progressScreen.js';
import AboutUs from '../screens/settings/aboutUs';
import Settings from '../screens/settings/settings.js';
import { CommonActions } from '@react-navigation/native';
import ComingSoon from '../components/ComingSoon.js';
import { Platform } from 'react-native';
import appearance from '../screens/settings/appearance.js';
import Notifications from '../screens/notification/notifications.js';
import ManageAccount from '../screens/settings/manageAccount.js';
import Reports from '../screens/settings/reports.js';
import ShareApp from '../components/ShareApp.js';

const Stack = createNativeStackNavigator();
const Tab = createBottomTabNavigator();
const CheckInStack = createNativeStackNavigator();

const TAB_ROUTES = {
  HOME: 'Home',
  SETTINGS: 'Settings',
  PROGRESS: 'Progress',
  MYSUPPORT: 'Support',
};

const tabIconMap = {
  [TAB_ROUTES.HOME]: HomeIcon,
  [TAB_ROUTES.SETTINGS]: SettingsIcon,
  [TAB_ROUTES.PROGRESS]: ProgressIcon,
  [TAB_ROUTES.MYSUPPORT]: SupportIcon,
};
const deviceModel = DeviceInfo.getModel();
const isIphoneSE = deviceModel.includes('iPhone SE');

export const BottomNavigation = () => {
  return (
    <Tab.Navigator
      initialRouteName={TAB_ROUTES.HOME}
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused }) => {
          const IconComponent = tabIconMap[route.name];
          if (!IconComponent) return null;
          const iconColor = focused ? ypdGreen : ypdWhite;
          return (
            <IconComponent
              width={28}
              height={28}
              stroke={iconColor}
              style={{ marginTop: 50 }}
            />
          );
        },
        tabBarStyle: {
          backgroundColor: ypdTeal,
          height:
            Platform.OS === 'android'
              ? isIphoneSE
                ? 75
                : 63
              : isIphoneSE
                ? 75
                : 95,
          paddingTop: 10,
        },
        tabBarLabelStyle: {
          fontSize: 13,
          fontWeight: '600',
          paddingBottom: isSmallDevice ? 10 : 5,
        },
        headerShown: false,
        tabBarActiveTintColor: ypdWhite,
        tabBarInactiveTintColor: ypdWhite,
        tabBarHideOnKeyboard: true,
      })}
    >
      <Tab.Screen name={TAB_ROUTES.HOME} component={HomeScreen} />

      <Tab.Screen
        name={TAB_ROUTES.PROGRESS}
        component={CheckInStackScreen}
        listeners={({ navigation }) => ({
          tabPress: (e) => {
            if (navigation.isFocused()) {
              e.preventDefault();
              return;
            }
            e.preventDefault();
            navigation.dispatch(
              CommonActions.reset({
                index: 0,
                routes: [
                  {
                    name: TAB_ROUTES.PROGRESS,
                    state: {
                      routes: [{ name: 'ProgressScreen' }],
                    },
                  },
                ],
              }),
            );
          },
        })}
      />

      <Tab.Screen name={TAB_ROUTES.SETTINGS} component={Settings} />
      <Tab.Screen name={TAB_ROUTES.MYSUPPORT} component={ComingSoon} />
    </Tab.Navigator>
  );
};

const CheckInStackScreen = () => (
  <CheckInStack.Navigator
    screenOptions={{
      headerShown: false,
      animationEnabled: false,
    }}
    initialRouteName="ProgressScreen"
  >
    <CheckInStack.Screen name="OverallEffect" component={OverallEffect} />
    <CheckInStack.Screen
      name="IndividualEffects"
      component={IndividualEffects}
    />
    <CheckInStack.Screen name="SideEffects" component={SideEffects} />
    <CheckInStack.Screen
      name="OverallSideEffect"
      component={OverallSideEffect}
    />
    <CheckInStack.Screen name="LastDose" component={LastDose} />
    <CheckInStack.Screen name="ProgressScreen" component={ProgressScreen} />
  </CheckInStack.Navigator>
);

const HomeNav = ({ initialRouteName }) => {
  return (
    <Stack.Navigator
      initialRouteName={initialRouteName || 'MainIntro'}
      screenOptions={{
        animation: 'none',
      }}
    >
      <Stack.Screen
        name="MainIntro"
        component={MainIntro}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Step1"
        component={Step1}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Step2"
        component={Step2}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Step3"
        component={Step3}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Step4"
        component={Step4}
        options={{
          headerBackVisible: false,
          headerShown: false,
          gestureEnabled: false,
        }}
      />
      <Stack.Screen
        name="Step5"
        component={Step5}
        options={{
          headerBackVisible: false,
          headerShown: false,
          gestureEnabled: false,
        }}
      />
      <Stack.Screen
        name="Step6"
        component={Step6}
        options={{
          headerBackVisible: false,
          headerShown: false,
          gestureEnabled: false,
        }}
      />
      <Stack.Screen
        name="Step7"
        component={Step7}
        options={{
          headerBackVisible: false,
          headerShown: false,
          gestureEnabled: false,
        }}
      />
      <Stack.Screen
        name="WarningScreen"
        component={WarningScreen}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="SignUp"
        component={SignUp}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Login"
        component={Login}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="ConfirmSignUp"
        component={ConfirmSignUp}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="ResendSignUpCode"
        component={ResendSignUpCode}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="ResetPassword"
        component={ResetPassword}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="VerifyNewPassword"
        component={VerifyNewPassword}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="NotificationPermission"
        component={NotificationPermission}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Notifications"
        component={Notifications}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="AboutUs"
        component={AboutUs}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Appearance"
        component={appearance}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="WelcomeScreen"
        component={WelcomeScreen}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="SideEffects"
        component={SideEffects}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="OverallSideEffect"
        component={OverallSideEffect}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="OverallEffect"
        component={OverallEffect}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="IndividualEffects"
        component={IndividualEffects}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="LastDose"
        component={LastDose}
        options={{
          headerBackVisible: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="CloseAppWarning"
        component={CloseAppWarning}
        options={{
          gestureEnabled: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="HomeScreen"
        component={HomeScreen}
        options={{
          gestureEnabled: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="ProgressScreen"
        component={ProgressScreen}
        options={{
          gestureEnabled: false,
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="Tabs"
        component={BottomNavigation}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="ConfirmRash"
        component={ConfirmRash}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="StreakModal"
        component={StreakModal}
        options={{
          presentation: 'modal',
          headerShown: false,
        }}
      />
      <Stack.Screen
        name="CalendarModal"
        component={CalendarModal}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="MaxModalities"
        component={MaxModalities}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="ManageAccount"
        component={ManageAccount}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="Reports"
        component={Reports}
        options={{ headerShown: false }}
      />
      <Stack.Screen
        name="ShareApp"
        component={ShareApp}
        options={{ headerShown: false }}
      />
    </Stack.Navigator>
  );
};

export default HomeNav;
