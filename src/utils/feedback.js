import { getUserProfile, updateUserDetails } from '../api/profile';

const FEEDBACK_REMINDER_DAYS = process.env.FEEDBACK_REMINDER_DAYS || 15;

export const requestFeedback = async (userId) => {
  const currentDate = new Date();
  let canRequestFeedback = false;

  try {
    const userData = await getUserProfile(userId);
    let lastShownDate = userData?.feedbackModalShownAt || userData?.createdAt;
    lastShownDate = new Date(lastShownDate);

    const timeDifference = currentDate - lastShownDate;
    const daysSinceLastShown = Math.floor(
      timeDifference / (1000 * 60 * 60 * 24),
    );

    if (daysSinceLastShown >= FEEDBACK_REMINDER_DAYS) {
      canRequestFeedback = true;
    }
    return canRequestFeedback;
  } catch (error) {
    console.error('Error fetching last shown date:', error);
  }

  if (canRequestFeedback) {
    await updateUserDetails({
      userId: userId,
      feedbackModalShownAt: new Date(),
    });
    return true;
  } else return false;
};
