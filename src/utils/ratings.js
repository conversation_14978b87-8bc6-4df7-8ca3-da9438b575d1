import { getUserProfile, updateUserDetails } from '../api/profile';
import { listUserRatings } from '../api/ratings';

const MIN_RATING = process.env.MIN_RATING || 4;
const RATING_REMINDER_DAYS = process.env.RATING_REMINDER_DAYS || 15;

export const checkRatingModalAgainstLastShown = async (userId) => {
  let showModal = false;
  const currentDate = new Date();

  try {
    const userData = await getUserProfile(userId);

    let lastShownDate = userData?.ratingModalShownAt || userData?.createdAt;

    lastShownDate = new Date(lastShownDate);

    const timeDifference = currentDate - lastShownDate;
    const daysSinceLastShown = Math.floor(
      timeDifference / (1000 * 60 * 60 * 24),
    );

    if (daysSinceLastShown >= RATING_REMINDER_DAYS) {
      showModal = await checkRatingModalAgainstValue(userId);
    }
  } catch (error) {
    console.error('Error fetching last shown date:', error);
  }

  if (showModal) {
    await updateUserDetails({
      userId: userId,
      ratingModalShownAt: new Date(),
    });
    return true;
  } else return false;
};

export const checkRatingModalAgainstValue = async (
  userId,
  updateUser = false,
) => {
  let showModal;
  const userRatings = await listUserRatings(userId);

  if (!userRatings.length) {
    showModal = true;
  } else {
    const sortedItems = userRatings.sort(
      (a, b) => new Date(b.createdAt) - new Date(a.createdAt),
    );
    const lastRating = sortedItems[0].rating;
    showModal = lastRating < MIN_RATING;
  }

  if (updateUser && showModal) {
    await updateUserDetails({
      userId: userId,
      ratingModalShownAt: new Date(),
    });
  }
  return showModal;
};
