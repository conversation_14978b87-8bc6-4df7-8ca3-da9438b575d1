import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>View, View } from 'react-native';
import Heading from '../../components/Heading';
import { Fonts, resetLoadingState } from '../../utils';
import { getMonday, getStartOfWeek } from '../../utils/utils';
import styles from '../../assets/styles/HomeScreen';
import CustomButton from '../../components/CustomButton';
import { ypdWhite } from '../../utils/colors';
import { ThemeContext } from '../../context/ThemeContext';
import { useCallback, useContext, useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { useNavigation } from '@react-navigation/native';
import { useFocusEffect, useRoute } from '@react-navigation/native';
import { useSelector } from 'react-redux';
import {
  setSuggestions,
  setStreakDates,
  setCBDILastSuggestion,
  setCBDOLastSuggestion,
  setLastSuggestion,
  setTHCILastSuggestion,
  setTHCOLastSuggestion,
  setCheckinStats,
  setAllowCheckin,
} from '../../redux/slices/suggestionSlice';
import LocalNotificationService from '../../utils/localNotifactionService';
import { getSuggestions } from '../../api/suggestion';
import SuggestionPopup from '../../components/SuggestionPopup';
import DoseSuggestionCard from '../../components/DoseSuggestionCard';
import WhereToBuy from '../../assets/svgs/home/<USER>';

const HomeScreen = () => {
  const { theme } = useContext(ThemeContext);
  const dispatch = useDispatch();
  const navigation = useNavigation();
  const profile = useSelector((state) => state.profile);

  const route = useRoute();
  const { newSuggestion = false, newCheckin = false } = route.params || {};
  const [greetTime, setGreetTime] = useState('');

  const [isLoading, setIsLoading] = useState(false);
  const [suggestionPopup, setSuggestionPopup] = useState(newSuggestion);
  const [lastCheckinMap, setLastCheckinMap] = useState({});
  const [checkinPopup, setCheckinPopup] = useState(newCheckin);
  const [showTimeLockoutPopup, setShowTimeLockoutPopup] = useState(false);


  const showNotifications = useSelector(
    (state) => state.profile.showNotifications,
  );
  const dailyReminders = useSelector(
    (state) => state.notifications.dailyReminders,
  );

  const lastSuggestion = useSelector(
    (state) => state.suggestions.lastSuggestion || {},
  );
  const allowCheckin = useSelector((state) => state.suggestions.allowCheckin);

  const userId = useSelector((state) => state.profile.userId);

  const getGreeting = () => {
    const hours = new Date().getHours();

    if (hours >= 4 && hours < 12) {
      return 'Good morning';
    } else if (hours >= 12 && hours < 17) {
      return 'Good afternoon';
    } else if (hours >= 17 && hours < 19) {
      return 'Good evening';
    } else {
      return 'Welcome back';
    }
  };

  useEffect(() => {
    setGreetTime(getGreeting());
  }, []);

  useEffect(() => {
    if (showNotifications) {
      LocalNotificationService.requestPermission(true);
      LocalNotificationService.cancelNotification('1');
      LocalNotificationService.cancelNotification('2');
      if (dailyReminders) {
        LocalNotificationService.scheduleDailyReminder('morning');
        LocalNotificationService.scheduleDailyReminder('evening');
      }
    } else {
      LocalNotificationService.cancelAllLocalNotifications();
    }
  }, []);

  useEffect(() => {
    dispatch(setAllowCheckin(checkLockout(lastSuggestion)));
  }, [lastCheckinMap]);

  const checkLockout = (lastSuggestion) => {
    console.log('Checking time lockout.');

    const now = new Date();

    const thciLastSuggestionTime = lastCheckinMap['thci'];
    const thcoLastSuggestionTime = lastCheckinMap['thco'];
    const cbdiLastSuggestionTime = lastCheckinMap['cbdi'];
    const cbdoLastSuggestionTime = lastCheckinMap['cbdo'];

    const isWithinXHours = (suggestionTime, hours) => {
      const timeDifferenceInHours = (now - suggestionTime) / (1000 * 60 * 60);
      return timeDifferenceInHours <= hours;
    };

    const isLocked =
      isWithinXHours(thciLastSuggestionTime, 1) ||
      isWithinXHours(thcoLastSuggestionTime, 2) ||
      isWithinXHours(cbdiLastSuggestionTime, 1) ||
      isWithinXHours(cbdoLastSuggestionTime, 2);

    if (isLocked) {
      console.log('At least one checkin within the last 1/2 hours.');
      return false;
    }

    if (
      !lastSuggestion ||
      !lastSuggestion.suggestionAt ||
      lastSuggestion?.suggestionType === 'initial'
    ) {
      return true;
    }

    const suggestionTime = new Date(lastSuggestion.suggestionAt);
    const timeDifferenceInHours = (now - suggestionTime) / (1000 * 60 * 60);

    if (timeDifferenceInHours <= 2) {
      const oralSuggestion = ['oral', 'both'].includes(
        lastSuggestion.dosageType,
      );
      const inhaledSuggestion = ['inhaled', 'both'].includes(
        lastSuggestion.dosageType,
      );

      if (oralSuggestion) {
        console.log('Two-hour time lockout for oral modalities.');
        return false;
      }

      if (timeDifferenceInHours <= 1 && inhaledSuggestion) {
        console.log('One-hour time lockout for inhaled modalities.');
        return false;
      }

      if (timeDifferenceInHours <= 1 && !inhaledSuggestion) {
        console.log('Allow the check-in < 1 hour - no modalities.');
        return true;
      }

      console.log('Allow the check-in < 2 hours - no modalities.');
      return true;
    }

    console.log('Allow the check-in > 2 hours.');
    return true;
  };

  useEffect(() => {
    const interval = setInterval(() => {
      dispatch(setAllowCheckin(checkLockout(lastSuggestion)));
    }, 300000);

    return () => clearInterval(interval);
  }, [lastSuggestion]);

  useFocusEffect(
    useCallback(() => {
      const fetchSuggestions = async () => {
        console.log('Fetching suggestions.');

        const streakDates = new Set();

        try {
          let thci = null;
          let thco = null;
          let cbdi = null;
          let cbdo = null;

          const today = new Date();
          const todayStr = today.toISOString().split('T')[0];
          const weekStart = getStartOfWeek(today);
          const weekEnd = new Date(weekStart);
          weekEnd.setDate(weekStart.getDate() + 6);

          let dailyInitialRegular = 0;
          let weeklyInitialRegular = 0;
          let streakInitialRegular = new Set();

          let dailyCheckin = 0;
          let weeklyCheckin = 0;
          let streakCheckin = new Set();

          const lastCheckins = {
            thci: null,
            thco: null,
            cbdi: null,
            cbdo: null,
          };

          const suggestions = await getSuggestions(userId);

          dispatch(setSuggestions(suggestions));

          for (const suggestion of suggestions) {
            const { cannabisType, dosageType } = suggestion;

            if (['both', 'thc'].includes(cannabisType)) {
              if (!thci && ['both', 'inhaled'].includes(dosageType) && !thci) {
                thci = suggestion;
              }
              if (!thco && ['both', 'oral'].includes(dosageType) && !thco) {
                thco = suggestion;
              }
            }

            if (['both', 'cbd'].includes(cannabisType)) {
              if (!cbdi && ['both', 'inhaled'].includes(dosageType) && !cbdi) {
                cbdi = suggestion;
              }
              if (!cbdo && ['both', 'oral'].includes(dosageType) && !cbdo) {
                cbdo = suggestion;
              }
            }

            if (suggestion.checkinAt) {
              const checkinDate = new Date(suggestion.checkinAt);
              const checkinStr = checkinDate.toISOString().split('T')[0];

              const isInThisWeek =
                checkinDate >= weekStart && checkinDate <= weekEnd;

              if (['initial', 'regular'].includes(suggestion.suggestionType)) {
                if (checkinStr === todayStr) dailyInitialRegular++;
                if (isInThisWeek) weeklyInitialRegular++;
                if (isInThisWeek) streakInitialRegular.add(checkinStr);
              }

              if (suggestion.suggestionType === 'checkin') {
                const modality = JSON.parse(suggestion.checkinType)['M'][
                  'modality'
                ]['S'];
                if (!lastCheckins[modality]) {
                  lastCheckins[modality] = checkinDate;
                }

                if (checkinStr === todayStr) dailyCheckin++;
                if (isInThisWeek) weeklyCheckin++;
                if (isInThisWeek) streakCheckin.add(checkinStr);
              }

              streakDates.add(checkinStr);
            }
          }

          const dailyCheckins =
            dailyCheckin > dailyInitialRegular
              ? dailyCheckin
              : dailyInitialRegular;
          const weeklyCheckins =
            weeklyCheckin > weeklyInitialRegular
              ? weeklyCheckin
              : weeklyInitialRegular;
          const weeklyStreaks =
            streakInitialRegular.size > streakCheckin.size
              ? streakInitialRegular.size
              : streakCheckin.size;

          const streakByWeek = {};

          for (const dateStr of streakDates) {
            const mondayStr = getMonday(dateStr);

            if (!streakByWeek[mondayStr]) {
              streakByWeek[mondayStr] = new Set();
            }

            streakByWeek[mondayStr].add(dateStr);
          }

          let fullStreakWeeks = 0;

          for (const dateSet of Object.values(streakByWeek)) {
            if (dateSet.size === 7) {
              fullStreakWeeks++;
            }
          }

          dispatch(
            setCheckinStats({
              dailyCheckins,
              weeklyCheckins,
              weeklyStreaks,
              fullStreakWeeks,
            }),
          );

          const lastSuggestion =
            suggestions.find((s) => s.suggestionType !== 'checkin') || {};

          dispatch(setAllowCheckin(checkLockout(lastSuggestion)));

          // console.log('lastSuggestion', lastSuggestion);
          // console.log('streakDates', streakDates);
          // console.log('lastCheckins', lastCheckins);

          setLastCheckinMap(lastCheckins);
          dispatch(setStreakDates(Array.from(streakDates)));

          dispatch(setLastSuggestion(lastSuggestion));
          dispatch(setTHCILastSuggestion(thci || lastSuggestion));
          dispatch(setTHCOLastSuggestion(thco || lastSuggestion));
          dispatch(setCBDILastSuggestion(cbdi || lastSuggestion));
          dispatch(setCBDOLastSuggestion(cbdo || lastSuggestion));

          if (lastSuggestion.userDisabled || lastSuggestion.isBELimitExceeded) {
            setSuggestionPopup(false);
            setCheckinPopup(false);
            navigation.navigate('MaxModalities');
          }
        } catch (err) {
          console.error('Error fetching suggestions:', err);
          dispatch(setSuggestions([]));
        }
      };

      if (userId) {
        fetchSuggestions();
      }
    }, [userId, dispatch]),
  );

  resetLoadingState(setIsLoading);

  const handleFeelingPress = () => {
    if (allowCheckin) {
      setIsLoading(true);

      navigation.navigate('Progress', {
        screen: 'OverallEffect',
      });
    } else {
      setShowTimeLockoutPopup(true);
    }
  };

  const handleTimeLockoutPopupClose = () => {
    setShowTimeLockoutPopup(false);
  };

  const handleSuggestionPopup = () => {
    setSuggestionPopup(false);
  };

  const handleCheckinPopup = () => {
    setCheckinPopup(false);
  };

  const createdAt = new Date(profile?.createdAt);
  const now = new Date();

  // difference in ms
  const diffMs = now - createdAt;
  const diffHours = diffMs / (1000 * 60 * 60);

  const isNewProfile = diffHours <= 1;

  return (
    <SafeAreaView
      style={[styles.mainWrapper, { backgroundColor: theme.colors.background }]}
    >
      <ScrollView
        showsVerticalScrollIndicator={false}
        contentContainerStyle={{ flexGrow: 1 }}
      >
        <View>
          <View style={{ padding: 10 }}>
            {isNewProfile ? (
              <>
                <Heading
                  text="Welcome!"
                  size="lg"
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.text}
                />
                <View style={styles.content}>
                  <Heading
                    text={`Your starting doses are ready.
                    Keep track of each dose, how you’re feeling, report any side effects—this helps the app tailor your dosage to what works best for you.`}
                    size="md"
                    fontFamily={Fonts.MEDIUM}
                    color={theme.colors.text}
                  />
                </View>
              </>
            ) : (
              <>
                <Heading
                  text={`Hi, ${profile?.name}! ` || 'Hello'}
                  size="lg"
                  fontFamily={Fonts.MEDIUM}
                  color={theme.colors.text}
                />
                <View style={styles.content}>
                  <Heading
                    text="Keep track of each dose, how you’re feeling, report any side effects."
                    size="md"
                    fontFamily={Fonts.MEDIUM}
                    color={theme.colors.text}
                  />
                </View>
              </>
            )}
          </View>
          <DoseSuggestionCard lastCheckinMap={lastCheckinMap} />
          <View
            style={{
              alignItems: 'center',
              justifyContent: 'center',
            }}
          >
            <CustomButton
              title="Let us know how you are feeling!"
              variant={
                allowCheckin
                  ? theme.dark
                    ? 'LetUsKnow'
                    : 'purple'
                  : 'stepsgreenoutline'
              }
              height={50}
              fontWeight="bold"
              textColor={allowCheckin ? ypdWhite : theme.colors.text}
              onPress={handleFeelingPress}
              width={320}
            />
          </View>
          <View
            style={[
              styles.whereToBuyContainer,
              { backgroundColor: theme.colors.whereToByBG },
            ]}
          >
            <View style={styles.whereToBuyText}>
              <Heading
                text="Where to Buy"
                size="sm"
                fontFamily={Fonts.BOLD}
                color={theme.colors.text}
              />
              <Heading
                text="Find products near you."
                size="xssmall"
                color={theme.colors.link}
                style={{
                  textDecorationLine: 'underline',
                  textDecorationColor: theme.colors.link,
                }}
              />
            </View>
            <View>
              <WhereToBuy />
            </View>
          </View>
          <SuggestionPopup
            visible={suggestionPopup}
            onClose={handleSuggestionPopup}
            isClose={false}
            data={[
              'Use your best judgment to decide how you use, alter, or ignore this suggestion.',
              'Give any cannabis by mouth 2 hours to work.',
              'Give any inhaled cannabis 1 hour to work.',
              'If you experience any problems or side effects, call 911 or go to your nearest hospital emergency room.',
            ]}
          />
          <SuggestionPopup
            visible={showTimeLockoutPopup}
            onClose={handleTimeLockoutPopupClose}
            data={[
              'We should wait for an hour or two to let the current suggestion take full effect.',
              'Please come back later, then you can check in for new suggestion',
            ]}
            isClose={true}
          />
          <SuggestionPopup
            visible={checkinPopup}
            onClose={handleCheckinPopup}
            isClose={false}
            cannabisType={route.params?.cannabisType}
            dosageType={route.params?.dosageType}
          />
        </View>
      </ScrollView>
    </SafeAreaView>
  );
};

export default HomeScreen;
