export const getProfile = /* GraphQL */ `
  query GetProfile($userId: String!) {
    getProfile(userId: $userId) {
      userId
      platform
      release
      email
      username
      name
      age
      gender
      problems
      customProblems
      sideEffects
      customSideEffects
      initialDose
      showNotifications
      privacyTermsAccepted
      safetyCheckAccepted
      darkModeModalIntroduced
      dosageType
      cannabisType
      ratingModalShownAt
      feedbackModalShownAt
      confirmationCodeCount
      confirmationCodeSentAt
      createdAt
      updatedAt
      __typename
    }
  }
`;

export const suggestionsByUserId = /* GraphQL */ `
  query SuggestionsByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelSuggestionFilterInput
    $limit: Int
    $nextToken: String
  ) {
    suggestionsByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        release
        username
        platform
        thco
        thci
        cbdo
        cbdi
        rthco
        rthci
        rcbdo
        rcbdi
        notes
        strain
        effects
        sideEffects
        overallEffect
        overallSideEffect
        loopN
        checkinType
        suggestionType
        dosageType
        cannabisType
        problemSetType
        userQuits
        userDisabled
        hitCBDMax
        hitTHCMax
        isBELimitExceeded
        createdAt
        updatedAt
        checkinAt
        suggestionAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;

export const listReports = /* GraphQL */ `
  query ListReports(
    $filter: ModelReportFilterInput
    $limit: Int
    $nextToken: String
  ) {
    listReports(filter: $filter, limit: $limit, nextToken: $nextToken) {
      items {
        name
        release
        platform
        username
        reportURL
        reportType
        slackResponseURL
        endDate
        startDate
        createdAt
        id
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;

export const userRatingsByUserId = /* GraphQL */ `
  query UserRatingsByUserId(
    $userId: String!
    $sortDirection: ModelSortDirection
    $filter: ModelUserRatingFilterInput
    $limit: Int
    $nextToken: String
  ) {
    userRatingsByUserId(
      userId: $userId
      sortDirection: $sortDirection
      filter: $filter
      limit: $limit
      nextToken: $nextToken
    ) {
      items {
        id
        userId
        username
        rating
        platform
        release
        comment
        createdAt
        updatedAt
        __typename
      }
      nextToken
      __typename
    }
  }
`;
