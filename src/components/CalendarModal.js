import { useCallback, useContext, useEffect, useState } from 'react';
import { View, TouchableWithoutFeedback, Keyboard } from 'react-native';
import CustomButton from './CustomButton';
import Heading from './Heading';
import CannabisDropDown from '../screens/profileBuilding/cannabisDropdown';
import { Fonts, ypdTextGrey, getDoseConfig } from '../utils';
import styles from '../assets/styles/CalendarModal.scss';
import BackButton from './BackButton';
import { SafeAreaView } from 'react-native-safe-area-context';
import {
  useFocusEffect,
  useNavigation,
  useRoute,
} from '@react-navigation/native';
import { KeyboardAwareScrollView } from 'react-native-keyboard-aware-scroll-view';
import Input from './Input';
import CalendarComponent from './CalendarComponent';
import { useSelector } from 'react-redux';
import uuid from 'react-native-uuid';
import { createDoseSuggestion } from '../api/suggestion';
import LocalNotificationService from '../utils/localNotifactionService';
import { recordEvent } from '../api/events';
import { ThemeContext } from '../context/ThemeContext';

const CalendarModal = () => {
  const navigation = useNavigation();
  const { theme } = useContext(ThemeContext);

  const route = useRoute();
  const { cannabisType, dosageType, modality, lockoutHours } =
    route.params || {};

  const [screenTime, setScreenTime] = useState(null);
  const [loading, setLoading] = useState(false);
  const [note, setNote] = useState('');
  const [currentDate, setCurrentDate] = useState(
    new Date().toISOString().split('T')[0],
  );

  const profile = useSelector((state) => state.profile);

  const initialDose = useSelector((state) => state.profile.initialDose);

  const individualNotifications = useSelector(
    (state) => state.profile.individualNotifications,
  );

  const lastSuggestion = useSelector(
    (state) => state.suggestions.lastSuggestion,
  );

  const thci = useSelector(
    (state) => state.suggestions.thciLastSuggestion?.thci,
  );
  const thco = useSelector(
    (state) => state.suggestions.thcoLastSuggestion?.thco,
  );
  const cbdi = useSelector(
    (state) => state.suggestions.cbdiLastSuggestion?.cbdi,
  );
  const cbdo = useSelector(
    (state) => state.suggestions.cbdoLastSuggestion?.cbdo,
  );

  const doseConfig = getDoseConfig(
    thci || initialDose?.thcVape,
    thco || initialDose?.thcOral,
    cbdi || initialDose?.cbdVape,
    cbdo || initialDose?.cbdOral,
  );
  const normalizedCannabisType = cannabisType?.toUpperCase();
  const config = doseConfig[`${normalizedCannabisType} ${dosageType}`];
  const [selectedDosage, setSelectedDosage] = useState(config.lastDose);

  const handleSelection = (value) => {
    setSelectedDosage(value);
  };
  const [ready, setReady] = useState(false);

  useEffect(() => {
    const timeout = setTimeout(() => setReady(true), 50);
    return () => clearTimeout(timeout);
  }, []);

  useFocusEffect(
    useCallback(() => {
      setScreenTime(new Date());
    }, []),
  );

  const saveCheckInEvent = async () => {
    const createdAt = new Date().toISOString();
    const attributes = {
      currentScreen: 'checkin',
      screenTime: (new Date() - screenTime) / 1000,
    };
    await recordEvent(
      'Checkin Event',
      profile.username,
      true,
      '#CH1',
      attributes,
      createdAt,
    );
  };

  const handleConfirm = async () => {
    setLoading(true);
    const previousValue = { thci, thco, cbdi, cbdo }[modality];

    const suggestionData = {
      rthci: modality === 'thci' ? selectedDosage : thci,
      rthco: modality === 'thco' ? selectedDosage : thco,
      rcbdi: modality === 'cbdi' ? selectedDosage : cbdi,
      rcbdo: modality === 'cbdo' ? selectedDosage : cbdo,
      id: uuid.v4(),
      notes: note,
      userId: profile.userId,
      username: profile.username,
      suggestionType: 'checkin',
      checkinType: JSON.stringify({
        modality,
        previousValue,
        selectedDosage,
        sameValue: previousValue === selectedDosage,
      }),
      dosageType: profile.dosageType,
      cannabisType: profile.cannabisType,
      hitCBDMax: lastSuggestion?.hitCBDMax,
      hitTHCMax: lastSuggestion?.hitTHCMax,
      loopN: (lastSuggestion?.loopN ?? 0) + 1,
      isBELimitExceeded: lastSuggestion?.isBELimitExceeded,
    };

    await createDoseSuggestion(suggestionData);
    await new Promise((resolve) => setTimeout(resolve, 3000));

    setSelectedDosage(null);
    setNote('');

    const isNotificationEnabled = individualNotifications?.[modality] ?? true;
    if (isNotificationEnabled) {
      await LocalNotificationService.requestPermission(true);
      await LocalNotificationService.scheduleLockoutNotification(
        modality,
        lockoutHours,
      );
    }
    await saveCheckInEvent();
    navigation.reset({
      index: 0,
      routes: [
        {
          name: 'Tabs',
          state: {
            index: 0,
            routes: [
              {
                name: 'Home',
                params: {
                  newCheckin: true,
                  cannabisType,
                  dosageType,
                },
              },
            ],
          },
        },
      ],
    });
  };

  return (
    <SafeAreaView
      style={[styles.scrollView, { backgroundColor: theme.colors.background }]}
    >
      {ready ? (
        <TouchableWithoutFeedback onPress={Keyboard.dismiss}>
          <KeyboardAwareScrollView
            enableOnAndroid={true}
            keyboardShouldPersistTaps="handled"
            extraScrollHeight={50}
            contentContainerStyle={{ flexGrow: 1 }}
          >
            <View style={styles.mainContainer}>
              <CalendarComponent
                initialDate={currentDate}
                onDateChange={setCurrentDate}
                initialViewMode="week"
                isLocked={true}
              />

              <View style={styles.sideEffectsContainer}>
                <View style={styles.firstView}>
                  <View style={styles.imageContainer}>{config.svg}</View>
                  <View style={styles.textContainer}>
                    <Heading
                      text={config.heading}
                      fontSize={18}
                      fontFamily={Fonts.MEDIUM}
                      color={theme.colors.text}
                    />
                    <Heading
                      text={config.subHeading}
                      fontSize={18}
                      fontFamily={Fonts.MEDIUM}
                      style={styles.subHeading}
                      color={theme.colors.text}
                    />
                    <CannabisDropDown
                      data={[
                        ...config.data,
                        ...(!config.data.some(
                          (item) => item.value === selectedDosage,
                        )
                          ? [
                              {
                                label: `${selectedDosage || 0} ${dosageType === 'oral' ? 'mg' : 'puff(s)'}`,
                                value: selectedDosage || 0,
                              },
                            ]
                          : []),
                      ].sort((a, b) => a.value - b.value)}
                      placeholder={`0 ${dosageType === 'oral' ? 'mg' : 'puff'}`}
                      value={selectedDosage}
                      onValueChange={handleSelection}
                      disabled={loading}
                    />
                  </View>
                </View>

                <View style={styles.bottomWrapper}>
                  <Input
                    value={note}
                    onChangeText={setNote}
                    placeholder="Include anything you'd like to note"
                    style={[
                      styles.textArea,
                      note ? styles.highlightedBorder : null,
                    ]}
                    height={100}
                    borderRadius={10}
                    multiline={true}
                    textAlignVertical="top"
                    hasValue={!!note}
                    disabled={loading}
                  />

                  <CustomButton
                    title="Confirm dose taken"
                    width="100%"
                    height="50"
                    variant="newGreen"
                    onPress={handleConfirm}
                    activityIndicator={loading}
                    disabled={!selectedDosage || loading}
                  />
                </View>
              </View>
              <View style={styles.backButton}>
                <BackButton
                  disabled={loading}
                  onBackPress={() => navigation.navigate('Tabs')}
                />
              </View>
            </View>
          </KeyboardAwareScrollView>
        </TouchableWithoutFeedback>
      ) : null}
    </SafeAreaView>
  );
};

export default CalendarModal;
