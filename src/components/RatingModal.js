import { useState, useContext } from 'react';
import {
  Modal,
  View,
  TouchableOpacity,
  Linking,
  Platform,
  Text,
} from 'react-native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import Heading from './Heading';
import { ThemeContext } from '../context/ThemeContext';
import Input from './Input';
import styles from '../assets/styles/ratingModal';
import { ypdGreen } from '../utils/colors';
import CustomButton from './CustomButton';
import { useSelector } from 'react-redux';
import { createNewRating } from '../api/ratings';

const RatingModal = ({ visible, setModalVisible }) => {
  const { theme } = useContext(ThemeContext);
  const profileData = useSelector((state) => state.profile);

  const [comment, setComment] = useState('');
  const [showAlert, setShowAlert] = useState(false);
  const [selectedStars, setSelectedStars] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const appStoreUrl = `https://apps.apple.com/us/app/${process.env.APPLE_STORE_ID}`;
  const playStoreUrl = `https://play.google.com/store/apps/details?id=${process.env.ANDROID_PACKAGE_NAME}`;

  const handleStarPress = (index) => {
    setSelectedStars(index + 1);
  };

  const closeModal = () => {
    setComment('');
    setSelectedStars(0);
    setShowAlert(false);
    setModalVisible(false);
  };

  const openAppStore = () => {
    const storeUrl = Platform.OS === 'ios' ? appStoreUrl : playStoreUrl;
    Linking.openURL(storeUrl).catch((err) =>
      console.error('Failed to open store', err),
    );
  };

  const handleSubmit = async () => {
    if (isSubmitting) return;
    setIsSubmitting(true);
    try {
      const input = {
        comment,
        rating: selectedStars,
        userId: profileData.userId,
        username: profileData.username,
      };
      await createNewRating(input);
      setShowAlert(true);
      setTimeout(() => {
        if (selectedStars === 4 || selectedStars === 5) {
          openAppStore();
        }
        closeModal();
      }, 2200);
    } catch (error) {
      console.error('Error submitting rating:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Modal visible={visible} transparent animationType="none">
      <View style={styles.modalContainer}>
        {showAlert ? (
          <View style={styles.alertBox}>
            <Icon name="check-circle" size={60} color={ypdGreen} />
            <Heading text="Thank You!" size="md" color={ypdGreen} />
            <Text style={{ color: theme.colors.text, marginTop: 5 }}>
              Your rating has been submitted successfully.
            </Text>
          </View>
        ) : (
          <>
            <View
              style={[
                styles.modalContent,
                { backgroundColor: theme.colors.background },
              ]}
            >
              <Icon
                style={styles.closeButton}
                onPress={() => closeModal()}
                name="close"
                size={24}
                color={theme.colors.text}
              />

              <Heading text="Rate YPD" size="md" color={theme.colors.text} />

              <View style={styles.starsContainer}>
                {[...Array(5)].map((_, index) => (
                  <TouchableOpacity
                    key={index}
                    onPress={() => handleStarPress(index)}
                  >
                    <Icon
                      name={index < selectedStars ? 'star' : 'star-border'}
                      size={40}
                      color={ypdGreen}
                    />
                  </TouchableOpacity>
                ))}
              </View>

              <Input
                style={styles.input}
                placeholder="Add a comment..."
                placeholderTextColor={theme.colors.textSecondary}
                value={comment}
                onChangeText={setComment}
                multiline={true}
                textAlignVertical="top"
                height={100}
                borderRadius={10}
              />

              <CustomButton
                title="Submit"
                onPress={handleSubmit}
                disabled={!selectedStars || !comment || isSubmitting}
                activityIndicator={isSubmitting}
              />
            </View>
          </>
        )}
      </View>
    </Modal>
  );
};

export default RatingModal;
