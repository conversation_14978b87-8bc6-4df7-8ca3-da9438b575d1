import React from 'react';
import { useContext } from 'react';
import {
  TouchableOpacity,
  Text,
  View,
  Image,
  ActivityIndicator,
} from 'react-native';
import styles from '../assets/styles/CustomButtonStyles';
import { ypdBlack, ypdOldGreen, ypdWhite } from '../utils/colors';
import { IconButton } from 'react-native-paper';
import Ionicons from 'react-native-vector-icons/Ionicons';
import { Fonts } from '../utils';
import { ThemeContext } from '../context/ThemeContext';

const CustomButton = ({
  onPress,
  title,
  variant = 'green',
  style,
  textStyle,
  alignSelf,
  alignItems = 'center',
  justifyContent = 'center',
  fontFamily = Fonts.REGULAR,
  fontSize = 16,
  disabled = false,
  lockButton = false,
  width = 343,
  height = 50,
  iconSource = null,
  iconComponent = null,
  ioniconName = null,
  iconDividerColor,
  iconColor,
  iconStyle,
  flexDirection = 'row',
  color = ypdBlack,
  size = 24,
  svgIcon = null,
  activityIndicator,
  lockText = null,
  textColor,
  borderRadius = 33,
}) => {
  const { theme } = useContext(ThemeContext);
  const buttonStyles = {
    green: styles.primaryButton,
    newGreen: styles.newGreenButton,
    blue: styles.secondaryButton,
    teal: theme.dark ? styles.tealButtonDark : styles.tealButton,
    purple: styles.purpleButton,
    grey: styles.greyButton,
    outline: theme.dark ? styles.outlineButtonDark : styles.outlineButton,
    lightoutline: styles.lightOutlineButton,
    greenoutline: theme.dark
      ? styles.greenOutlineButtonDark
      : styles.greenOutlineButton,
    stepsgreenoutline: styles.greenOutlineButton,
    highlightblue: styles.highlightBlueButton,
    transparent: styles.transparent,
    delete: styles.deleteButton,
    LetUsKnow: styles.letUsKnowButton,
  };

  const renderIcon = () => {
    if (svgIcon) {
      return (
        <View style={[iconStyle]}>
          {React.cloneElement(svgIcon, {
            width: size,
            height: size,
            fill: iconColor || ypdBlack,
          })}
        </View>
      );
    }
    if (iconComponent) {
      return (
        <>
          <IconButton
            icon={iconComponent}
            size={size}
            iconColor={iconColor}
            style={[styles.iconButtonStyle, iconStyle]}
          />
          {iconDividerColor && (
            <View
              style={[styles.divider, { backgroundColor: iconDividerColor }]}
            />
          )}
        </>
      );
    }

    if (ioniconName) {
      return (
        <>
          <Ionicons
            name={ioniconName}
            size={size}
            color={iconColor}
            style={iconStyle}
          />
          {iconDividerColor && (
            <View
              style={[styles.divider, { backgroundColor: iconDividerColor }]}
            />
          )}
        </>
      );
    }
    if (iconSource) {
      return (
        <Image
          source={iconSource}
          style={[
            styles.iconButtonStyle,
            iconStyle,
            { width: size, height: size },
          ]}
          resizeMode="contain"
        />
      );
    }
    return null;
  };

  return (
    <TouchableOpacity
      style={[
        buttonStyles[variant] || styles.primaryButton,
        disabled && styles.disabledButton,
        style,
        { width, height, alignSelf, borderRadius },
      ]}
      onPress={lockButton ? undefined : onPress}
      disabled={disabled}
    >
      <View
        style={{
          flexDirection,
          alignItems: alignItems,
          justifyContent: justifyContent,
          gap: 8,
        }}
      >
        {!activityIndicator && renderIcon()}
        {activityIndicator ? (
          <ActivityIndicator color={ypdOldGreen} />
        ) : (
          <View style={{ alignItems: 'center' }}>
            <Text
              style={[
                {
                  fontSize,
                  color:
                    disabled && theme.dark
                      ? ypdWhite
                      : variant === 'outline' && theme.dark
                        ? ypdWhite
                        : textColor || color,
                  fontFamily,
                },
                styles.continueButtonText,
                textStyle,
                disabled && styles.disabledText,
              ]}
            >
              {title}
            </Text>
            {lockText && (
              <Text
                style={[
                  styles.continueButtonText,
                  { fontSize: 12, color: ypdBlack, marginTop: 4 },
                ]}
              >
                {lockText}
              </Text>
            )}
          </View>
        )}
      </View>
    </TouchableOpacity>
  );
};

export default CustomButton;
